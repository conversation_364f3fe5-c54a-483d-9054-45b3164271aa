{"rustc": 3062648155896360161, "features": "[\"compression\"]", "declared_features": "[\"compression\", \"config-json5\", \"config-toml\", \"custom-protocol\", \"isolation\", \"tracing\"]", "target": 4649449654257170297, "profile": 2225463790103693989, "path": 9712455429411037471, "deps": [[373107762698212489, "proc_macro2", false, 11125204075046863589], [3412097196613774653, "tauri_codegen", false, 17898857399366870970], [13077543566650298139, "heck", false, 9103663505226094303], [17233053221795943287, "tauri_utils", false, 10176838432830745087], [17332570067994900305, "syn", false, 6886913303237127862], [17990358020177143287, "quote", false, 15499431614064153038]], "local": [{"CheckDepInfo": {"dep_info": "debug\\.fingerprint\\tauri-macros-de4465dcd8d4d4eb\\dep-lib-tauri_macros", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}