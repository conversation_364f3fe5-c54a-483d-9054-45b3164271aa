{"rustc": 3062648155896360161, "features": "[\"config-json\", \"default\"]", "declared_features": "[\"codegen\", \"config-json\", \"config-json5\", \"config-toml\", \"default\", \"isolation\", \"quote\", \"tauri-codegen\"]", "target": 1006236803848883740, "profile": 2225463790103693989, "path": 5900569307827957037, "deps": [[4352886507220678900, "serde_json", false, 10385418511734645366], [4824857623768494398, "cargo_toml", false, 18313847334710020408], [4899080583175475170, "semver", false, 17625887480789864879], [5165059047667588304, "tauri_winres", false, 17517378590300992563], [6913375703034175521, "schemars", false, 13845663403254456321], [7170110829644101142, "json_patch", false, 6027096731772322571], [9293239362693504808, "glob", false, 17651112888402818931], [9689903380558560274, "serde", false, 8372695588916261890], [11207653606310558077, "anyhow", false, 2795605351215414651], [12060164242600251039, "toml", false, 344541517394915406], [13077543566650298139, "heck", false, 9103663505226094303], [15622660310229662834, "walkdir", false, 8117565403321995056], [16928111194414003569, "dirs", false, 13442575851294566801], [17233053221795943287, "tauri_utils", false, 10176838432830745087]], "local": [{"CheckDepInfo": {"dep_info": "debug\\.fingerprint\\tauri-build-354240bf60a6b056\\dep-lib-tauri_build", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}