{"rustc": 3062648155896360161, "features": "[]", "declared_features": "[\"backtrace\", \"deadlock_detection\", \"nightly\", \"petgraph\", \"thread-id\"]", "target": 12558056885032795287, "profile": 15657897354478470176, "path": 6993089196454052125, "deps": [[3666196340704888985, "smallvec", false, 17657741371438365037], [4269498962362888130, "build_script_build", false, 9382946075704478145], [7843059260364151289, "cfg_if", false, 6955258511737294775], [14322346790800707264, "windows_targets", false, 5687959416366163189]], "local": [{"CheckDepInfo": {"dep_info": "debug\\.fingerprint\\parking_lot_core-bb87062d79598175\\dep-lib-parking_lot_core", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}