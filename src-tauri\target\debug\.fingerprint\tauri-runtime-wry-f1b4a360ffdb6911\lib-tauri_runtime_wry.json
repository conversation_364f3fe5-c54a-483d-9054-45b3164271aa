{"rustc": 3062648155896360161, "features": "[\"common-controls-v6\", \"x11\"]", "declared_features": "[\"common-controls-v6\", \"default\", \"devtools\", \"macos-private-api\", \"macos-proxy\", \"objc-exception\", \"tracing\", \"unstable\", \"x11\"]", "target": 1901661049345253480, "profile": 2241668132362809309, "path": 8020507221556070678, "deps": [[376837177317575824, "softbuffer", false, 1837546574623960574], [2013030631243296465, "webview2_com", false, 18122369991519328561], [2172092324659420098, "tao", false, 17594216892018644211], [3722963349756955755, "once_cell", false, 4464787853665521902], [3899750328741010762, "build_script_build", false, 14409777034687062093], [4143744114649553716, "raw_window_handle", false, 3712108104936549517], [5404511084185685755, "url", false, 17090050064083276578], [5986029879202738730, "log", false, 10945036430207440385], [9010263965687315507, "http", false, 4556469098080637687], [14585479307175734061, "windows", false, 457994763200677128], [15302940006583996851, "wry", false, 8500274908423040383], [17233053221795943287, "tauri_utils", false, 56706992117261356], [18010483002580779355, "tauri_runtime", false, 9195745845183656088]], "local": [{"CheckDepInfo": {"dep_info": "debug\\.fingerprint\\tauri-runtime-wry-f1b4a360ffdb6911\\dep-lib-tauri_runtime_wry", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}