{"rustc": 3062648155896360161, "features": "", "declared_features": "", "target": 0, "profile": 0, "path": 0, "deps": [[12602218467368646123, "build_script_build", false, 8499221626644278046], [3239934230994155792, "build_script_build", false, 4559112573748326344], [14285978758320820277, "build_script_build", false, 9063906522573627874], [16429266147849286097, "build_script_build", false, 7109863438122861053]], "local": [{"RerunIfChanged": {"output": "debug\\build\\auto-cursor-d18ec1ad16d3c7e9\\output", "paths": ["tauri.conf.json", "capabilities", "pyBuild\\macos\\README.md", "pyBuild\\macos\\cursor_register"]}}, {"RerunIfEnvChanged": {"var": "TAURI_CONFIG", "val": null}}, {"RerunIfEnvChanged": {"var": "REMOVE_UNUSED_COMMANDS", "val": null}}], "rustflags": [], "config": 0, "compile_kind": 0}