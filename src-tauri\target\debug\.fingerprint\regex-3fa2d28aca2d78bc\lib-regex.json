{"rustc": 3062648155896360161, "features": "[\"default\", \"perf\", \"perf-backtrack\", \"perf-cache\", \"perf-dfa\", \"perf-inline\", \"perf-literal\", \"perf-onepass\", \"std\", \"unicode\", \"unicode-age\", \"unicode-bool\", \"unicode-case\", \"unicode-gencat\", \"unicode-perl\", \"unicode-script\", \"unicode-segment\"]", "declared_features": "[\"default\", \"logging\", \"pattern\", \"perf\", \"perf-backtrack\", \"perf-cache\", \"perf-dfa\", \"perf-dfa-full\", \"perf-inline\", \"perf-literal\", \"perf-onepass\", \"std\", \"unicode\", \"unicode-age\", \"unicode-bool\", \"unicode-case\", \"unicode-gencat\", \"unicode-perl\", \"unicode-script\", \"unicode-segment\", \"unstable\", \"use_std\"]", "target": 5796931310894148030, "profile": 15657897354478470176, "path": 6790020796633380333, "deps": [[2779309023524819297, "aho_corasick", false, 1319078703399576685], [7507008215594894126, "regex_syntax", false, 1996259084362911923], [15932120279885307830, "memchr", false, 589235425451624464], [16311927252525485886, "regex_automata", false, 7529188200664945227]], "local": [{"CheckDepInfo": {"dep_info": "debug\\.fingerprint\\regex-3fa2d28aca2d78bc\\dep-lib-regex", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}