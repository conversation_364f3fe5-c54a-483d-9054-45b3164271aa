{"rustc": 3062648155896360161, "features": "[\"brotli\", \"build\", \"cargo_metadata\", \"compression\", \"html-manipulation\", \"proc-macro2\", \"quote\", \"resources\", \"schema\", \"schemars\", \"swift-rs\", \"walkdir\"]", "declared_features": "[\"aes-gcm\", \"brotli\", \"build\", \"cargo_metadata\", \"compression\", \"config-json5\", \"config-toml\", \"getrandom\", \"html-manipulation\", \"isolation\", \"json5\", \"proc-macro2\", \"process-relaunch-dangerous-allow-symlink-macos\", \"quote\", \"resources\", \"schema\", \"schemars\", \"serialize-to-javascript\", \"swift-rs\", \"walkdir\"]", "target": 7530130812022395703, "profile": 2225463790103693989, "path": 16136054914536793718, "deps": [[373107762698212489, "proc_macro2", false, 11125204075046863589], [503635761244294217, "regex", false, 10349561990116727839], [1200537532907108615, "url<PERSON><PERSON>n", false, 1681661447803209661], [1678291836268844980, "brotli", false, 4300331577582835088], [2995469292676432503, "uuid", false, 951695421271510927], [4071963112282141418, "serde_with", false, 6643480301114630256], [4352886507220678900, "serde_json", false, 10385418511734645366], [4537297827336760846, "thiserror", false, 6762184113965494642], [4899080583175475170, "semver", false, 17625887480789864879], [5404511084185685755, "url", false, 806589115456336325], [5986029879202738730, "log", false, 1953282675189052718], [6606131838865521726, "ctor", false, 10778789883276638683], [6913375703034175521, "schemars", false, 13845663403254456321], [7170110829644101142, "json_patch", false, 6027096731772322571], [9010263965687315507, "http", false, 219411552066842355], [9293239362693504808, "glob", false, 17651112888402818931], [9689903380558560274, "serde", false, 8372695588916261890], [11207653606310558077, "anyhow", false, 2795605351215414651], [11655476559277113544, "cargo_metadata", false, 5505230586037484870], [11989259058781683633, "dunce", false, 132761268911456026], [12060164242600251039, "toml", false, 344541517394915406], [14232843520438415263, "html5ever", false, 11874487816476366813], [15088007382495681292, "kuchiki", false, 15525386609264511972], [15622660310229662834, "walkdir", false, 8117565403321995056], [15932120279885307830, "memchr", false, 589235425451624464], [17146114186171651583, "infer", false, 10170985794444701307], [17183029615630212089, "serde_untagged", false, 4190616566375709437], [17186037756130803222, "phf", false, 11694825372623029522], [17990358020177143287, "quote", false, 15499431614064153038]], "local": [{"CheckDepInfo": {"dep_info": "debug\\.fingerprint\\tauri-utils-0b78672b79e46e89\\dep-lib-tauri_utils", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}