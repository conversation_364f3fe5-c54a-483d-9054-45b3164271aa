{"rustc": 3062648155896360161, "features": "[\"default\", \"perf\", \"perf-backtrack\", \"perf-cache\", \"perf-dfa\", \"perf-inline\", \"perf-literal\", \"perf-onepass\", \"std\", \"unicode\", \"unicode-age\", \"unicode-bool\", \"unicode-case\", \"unicode-gencat\", \"unicode-perl\", \"unicode-script\", \"unicode-segment\"]", "declared_features": "[\"default\", \"logging\", \"pattern\", \"perf\", \"perf-backtrack\", \"perf-cache\", \"perf-dfa\", \"perf-dfa-full\", \"perf-inline\", \"perf-literal\", \"perf-onepass\", \"std\", \"unicode\", \"unicode-age\", \"unicode-bool\", \"unicode-case\", \"unicode-gencat\", \"unicode-perl\", \"unicode-script\", \"unicode-segment\", \"unstable\", \"use_std\"]", "target": 5796931310894148030, "profile": 2225463790103693989, "path": 6790020796633380333, "deps": [[2779309023524819297, "aho_corasick", false, 16319905179104342561], [7507008215594894126, "regex_syntax", false, 7203736078086048001], [15932120279885307830, "memchr", false, 8122729805812561546], [16311927252525485886, "regex_automata", false, 15322410018841557894]], "local": [{"CheckDepInfo": {"dep_info": "debug\\.fingerprint\\regex-499bf085604d56f0\\dep-lib-regex", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}