{"rustc": 3062648155896360161, "features": "[]", "declared_features": "[\"devtools\", \"macos-private-api\"]", "target": 10306386172444932100, "profile": 2241668132362809309, "path": 11105139912416421474, "deps": [[2013030631243296465, "webview2_com", false, 18122369991519328561], [4143744114649553716, "raw_window_handle", false, 3712108104936549517], [4352886507220678900, "serde_json", false, 16540324934345454808], [4537297827336760846, "thiserror", false, 12581723110571688579], [5404511084185685755, "url", false, 17090050064083276578], [7606335748176206944, "dpi", false, 4297561714799133127], [9010263965687315507, "http", false, 4556469098080637687], [9689903380558560274, "serde", false, 11450851296298991122], [14585479307175734061, "windows", false, 457994763200677128], [16727543399706004146, "cookie", false, 1546025980771397503], [17233053221795943287, "tauri_utils", false, 56706992117261356], [18010483002580779355, "build_script_build", false, 16698244333533178993]], "local": [{"CheckDepInfo": {"dep_info": "debug\\.fingerprint\\tauri-runtime-e4525053ebb1ddff\\dep-lib-tauri_runtime", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}