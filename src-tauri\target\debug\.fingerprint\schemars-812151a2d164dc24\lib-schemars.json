{"rustc": 3062648155896360161, "features": "[\"default\", \"derive\", \"indexmap\", \"preserve_order\", \"schemars_derive\", \"url\", \"uuid1\"]", "declared_features": "[\"arrayvec\", \"arrayvec05\", \"arrayvec07\", \"bigdecimal\", \"bigdecimal03\", \"bigdecimal04\", \"bytes\", \"chrono\", \"default\", \"derive\", \"derive_json_schema\", \"either\", \"enumset\", \"impl_json_schema\", \"indexmap\", \"indexmap1\", \"indexmap2\", \"preserve_order\", \"raw_value\", \"rust_decimal\", \"schemars_derive\", \"semver\", \"smallvec\", \"smol_str\", \"ui_test\", \"url\", \"uuid\", \"uuid08\", \"uuid1\"]", "target": 11155677158530064643, "profile": 2225463790103693989, "path": 12337168563859695695, "deps": [[2995469292676432503, "uuid1", false, 951695421271510927], [4352886507220678900, "serde_json", false, 10385418511734645366], [5404511084185685755, "url", false, 806589115456336325], [6913375703034175521, "build_script_build", false, 17572225141779419783], [6982418085031928086, "dyn_clone", false, 6577794489190731039], [9689903380558560274, "serde", false, 8372695588916261890], [14923790796823607459, "indexmap", false, 4867881608103497438], [16071897500792579091, "schemars_derive", false, 18317085503045162511]], "local": [{"CheckDepInfo": {"dep_info": "debug\\.fingerprint\\schemars-812151a2d164dc24\\dep-lib-schemars", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}