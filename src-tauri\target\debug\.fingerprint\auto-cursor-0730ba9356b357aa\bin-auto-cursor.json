{"rustc": 3062648155896360161, "features": "[]", "declared_features": "[]", "target": 8188219801901903984, "profile": 17672942494452627365, "path": 4942398508502643691, "deps": [[503635761244294217, "regex", false, 2754097746849034404], [530211389790465181, "hex", false, 14272251260812388198], [1996688857878793156, "<PERSON><PERSON><PERSON><PERSON>", false, 11730421531478757083], [2995469292676432503, "uuid", false, 8269864632713534960], [3239934230994155792, "tauri", false, 1688242307014108152], [4352886507220678900, "serde_json", false, 16540324934345454808], [7244058819997729774, "reqwest", false, 15005256150688402943], [8008191657135824715, "thiserror", false, 4998417403621411942], [8256202458064874477, "dirs", false, 12968899203507108618], [8324132117207348776, "rusqlite", false, 5525871908097623076], [8405603588346937335, "winreg", false, 15721091670357367292], [9689903380558560274, "serde", false, 11450851296298991122], [9857275760291862238, "sha2", false, 13350054013613037909], [9897246384292347999, "chrono", false, 5634903993770766332], [11207653606310558077, "anyhow", false, 3120685154372012934], [12602218467368646123, "auto_cursor_lib", false, 17867438977313199783], [12602218467368646123, "build_script_build", false, 18272150905319128140], [13208667028893622512, "rand", false, 17768093000990847522], [14285978758320820277, "tauri_plugin_fs", false, 17820526758176189550], [16429266147849286097, "tauri_plugin_opener", false, 11091324619347829202], [17531218394775549125, "tokio", false, 16899422864590576172], [18066890886671768183, "base64", false, 4259307100133726979]], "local": [{"CheckDepInfo": {"dep_info": "debug\\.fingerprint\\auto-cursor-0730ba9356b357aa\\dep-bin-auto-cursor", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}