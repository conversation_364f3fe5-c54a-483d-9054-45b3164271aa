{"rustc": 3062648155896360161, "features": "[\"alloc\", \"default\", \"getrandom\", \"getrandom_package\", \"libc\", \"rand_pcg\", \"small_rng\", \"std\"]", "declared_features": "[\"alloc\", \"default\", \"getrandom\", \"getrandom_package\", \"libc\", \"log\", \"nightly\", \"packed_simd\", \"rand_pcg\", \"serde1\", \"simd_support\", \"small_rng\", \"std\", \"stdweb\", \"wasm-bindgen\"]", "target": 8827111241893198906, "profile": 2225463790103693989, "path": 12602641851739412841, "deps": [[1333041802001714747, "rand_chacha", false, 17848117202337403710], [1740877332521282793, "rand_core", false, 9288895541836910408], [5170503507811329045, "getrandom_package", false, 9207728705461915151], [9875507072765444643, "rand_pcg", false, 15963827755202496410]], "local": [{"CheckDepInfo": {"dep_info": "debug\\.fingerprint\\rand-c406dc0c8da795bb\\dep-lib-rand", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}