{"rustc": 3062648155896360161, "features": "[\"brotli\", \"compression\"]", "declared_features": "[\"brotli\", \"compression\", \"config-json5\", \"config-toml\", \"isolation\"]", "target": 17460618180909919773, "profile": 2225463790103693989, "path": 445383579239114737, "deps": [[373107762698212489, "proc_macro2", false, 11125204075046863589], [1678291836268844980, "brotli", false, 1798231122162274048], [2995469292676432503, "uuid", false, 210148040987626204], [4352886507220678900, "serde_json", false, 6670023159960378529], [4537297827336760846, "thiserror", false, 7987120693536890622], [4899080583175475170, "semver", false, 17625887480789864879], [5404511084185685755, "url", false, 11798837371836397836], [7170110829644101142, "json_patch", false, 18002762306370759159], [7392050791754369441, "ico", false, 7835831792508631598], [9689903380558560274, "serde", false, 8372695588916261890], [9857275760291862238, "sha2", false, 9605160235899629911], [12687914511023397207, "png", false, 10978641369623199456], [13077212702700853852, "base64", false, 6177770844656299697], [15622660310229662834, "walkdir", false, 3961161375583282089], [17233053221795943287, "tauri_utils", false, 11092907689118060566], [17332570067994900305, "syn", false, 6886913303237127862], [17990358020177143287, "quote", false, 15499431614064153038]], "local": [{"CheckDepInfo": {"dep_info": "debug\\.fingerprint\\tauri-codegen-38f748f1cfac967b\\dep-lib-tauri_codegen", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}