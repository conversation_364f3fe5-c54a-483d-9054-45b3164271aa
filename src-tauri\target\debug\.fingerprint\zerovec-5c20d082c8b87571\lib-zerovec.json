{"rustc": 3062648155896360161, "features": "[\"alloc\", \"derive\", \"yoke\"]", "declared_features": "[\"alloc\", \"databake\", \"derive\", \"hashmap\", \"serde\", \"std\", \"yoke\"]", "target": 1825474209729987087, "profile": 2241668132362809309, "path": 10608391399329856557, "deps": [[9620753569207166497, "zerovec_derive", false, 15917644771104217534], [10706449961930108323, "yoke", false, 13664641069238592380], [17046516144589451410, "zerofrom", false, 12075730955471827118]], "local": [{"CheckDepInfo": {"dep_info": "debug\\.fingerprint\\zerovec-5c20d082c8b87571\\dep-lib-zerovec", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}