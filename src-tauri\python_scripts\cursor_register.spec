# -*- mode: python ; coding: utf-8 -*-
from PyInstaller.utils.hooks import collect_all

datas = [('E:\\前端\\auto-cursor\\src-tauri\\python_scripts/*.py', '.'), ('E:\\前端\\auto-cursor\\src-tauri\\python_scripts/email_tabs', 'email_tabs')]
binaries = []
hiddenimports = ['manual_register', 'cursor_register_manual', 'new_signup', 'cursor_auth', 'reset_machine_manual', 'get_user_token', 'account_manager', 'config', 'utils', 'email_tabs.email_tab_interface', 'email_tabs.tempmail_plus_tab', 'faker.providers.person', 'faker.providers.internet', 'faker.providers.lorem', 'requests.adapters', 'requests.packages.urllib3', 'urllib3.util.retry', 'urllib3.util.connection']
tmp_ret = collect_all('faker')
datas += tmp_ret[0]; binaries += tmp_ret[1]; hiddenimports += tmp_ret[2]
tmp_ret = collect_all('colorama')
datas += tmp_ret[0]; binaries += tmp_ret[1]; hiddenimports += tmp_ret[2]
tmp_ret = collect_all('requests')
datas += tmp_ret[0]; binaries += tmp_ret[1]; hiddenimports += tmp_ret[2]
tmp_ret = collect_all('DrissionPage')
datas += tmp_ret[0]; binaries += tmp_ret[1]; hiddenimports += tmp_ret[2]
tmp_ret = collect_all('dotenv')
datas += tmp_ret[0]; binaries += tmp_ret[1]; hiddenimports += tmp_ret[2]


a = Analysis(
    ['E:\\前端\\auto-cursor\\src-tauri\\python_scripts\\cursor_register_entry.py'],
    pathex=[],
    binaries=binaries,
    datas=datas,
    hiddenimports=hiddenimports,
    hookspath=[],
    hooksconfig={},
    runtime_hooks=[],
    excludes=[],
    noarchive=False,
    optimize=0,
)
pyz = PYZ(a.pure)

exe = EXE(
    pyz,
    a.scripts,
    a.binaries,
    a.datas,
    [],
    name='cursor_register',
    debug=False,
    bootloader_ignore_signals=False,
    strip=False,
    upx=True,
    upx_exclude=[],
    runtime_tmpdir=None,
    console=True,
    disable_windowed_traceback=False,
    argv_emulation=False,
    target_arch=None,
    codesign_identity=None,
    entitlements_file=None,
)
