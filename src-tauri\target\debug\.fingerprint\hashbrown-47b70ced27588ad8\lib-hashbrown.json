{"rustc": 3062648155896360161, "features": "[\"ahash\", \"allocator-api2\", \"default\", \"inline-more\"]", "declared_features": "[\"ahash\", \"alloc\", \"allocator-api2\", \"compiler_builtins\", \"core\", \"default\", \"equivalent\", \"inline-more\", \"nightly\", \"raw\", \"rayon\", \"rkyv\", \"rustc-dep-of-std\", \"rustc-internal-api\", \"serde\"]", "target": 9101038166729729440, "profile": 15657897354478470176, "path": 15452522703607766970, "deps": [[966925859616469517, "ahash", false, 13117012160754713123], [9150530836556604396, "allocator_api2", false, 3523981871411425705]], "local": [{"CheckDepInfo": {"dep_info": "debug\\.fingerprint\\hashbrown-47b70ced27588ad8\\dep-lib-hashbrown", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}