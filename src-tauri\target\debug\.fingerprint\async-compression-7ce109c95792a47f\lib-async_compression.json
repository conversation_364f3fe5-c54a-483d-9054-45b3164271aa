{"rustc": 3062648155896360161, "features": "[\"brotli\", \"flate2\", \"gzip\", \"tokio\", \"zlib\"]", "declared_features": "[\"all\", \"all-algorithms\", \"all-implementations\", \"brotli\", \"bzip2\", \"deflate\", \"deflate64\", \"flate2\", \"futures-io\", \"gzip\", \"liblzma\", \"libzstd\", \"lz4\", \"lzma\", \"tokio\", \"xz\", \"xz-parallel\", \"xz2\", \"zlib\", \"zstd\", \"zstd-safe\", \"zstdmt\"]", "target": 7068030942456847288, "profile": 1419271269840776899, "path": 5183952152418375113, "deps": [[1678291836268844980, "brotli", false, 6490841984202478381], [1906322745568073236, "pin_project_lite", false, 2544603237086067178], [7620660491849607393, "futures_core", false, 15780360714119166146], [9495956783373963623, "compression_codecs", false, 10871668115729844242], [13865646604939608930, "compression_core", false, 10963543668867509811], [15932120279885307830, "memchr", false, 589235425451624464], [17531218394775549125, "tokio", false, 3748829182340658219], [17772299992546037086, "flate2", false, 16972944683250358959]], "local": [{"CheckDepInfo": {"dep_info": "debug\\.fingerprint\\async-compression-7ce109c95792a47f\\dep-lib-async_compression", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}