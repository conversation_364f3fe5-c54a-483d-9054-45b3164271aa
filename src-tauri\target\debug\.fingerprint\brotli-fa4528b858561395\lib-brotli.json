{"rustc": 3062648155896360161, "features": "[\"alloc-stdlib\", \"std\"]", "declared_features": "[\"alloc-stdlib\", \"benchmark\", \"billing\", \"default\", \"disable-timer\", \"disallow_large_window_size\", \"external-literal-probability\", \"ffi-api\", \"float64\", \"floating_point_context_mixing\", \"no-stdlib-ffi-binding\", \"pass-through-ffi-panics\", \"seccomp\", \"sha2\", \"simd\", \"std\", \"validation\", \"vector_scratch_space\"]", "target": 8433163163091947982, "profile": 2225463790103693989, "path": 9966252075063135560, "deps": [[9611597350722197978, "alloc_no_stdlib", false, 10077205457471680552], [16413620717702030930, "brotli_decompressor", false, 1952986648251273499], [17470296833448545982, "alloc_stdlib", false, 16505464193796806018]], "local": [{"CheckDepInfo": {"dep_info": "debug\\.fingerprint\\brotli-fa4528b858561395\\dep-lib-brotli", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}