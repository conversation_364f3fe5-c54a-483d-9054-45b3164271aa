from abc import ABC, abstractmethod
import sys
import io

# 设置UTF-8编码输出
sys.stdout = io.TextIOWrapper(sys.stdout.buffer, encoding='utf-8')
sys.stderr = io.TextIOWrapper(sys.stderr.buffer, encoding='utf-8')

class EmailTabInterface(ABC):
    """Interface for email tab implementations"""
    
    @abstractmethod
    def refresh_inbox(self) -> None:
        """Refresh the email inbox"""
        pass
    
    @abstractmethod
    def check_for_cursor_email(self) -> bool:
        """Check if there is a new email from Cursor
        
        Returns:
            bool: True if new email found, False otherwise
        """
        pass
    
    @abstractmethod
    def get_verification_code(self) -> str:
        """Get the verification code from the email
        
        Returns:
            str: The verification code if available, empty string otherwise
        """
        pass
