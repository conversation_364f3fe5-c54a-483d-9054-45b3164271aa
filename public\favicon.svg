<svg width="32" height="32" viewBox="0 0 32 32" fill="none"
  xmlns="http://www.w3.org/2000/svg">
  <!-- 主背景圆 -->
  <circle cx="16" cy="16" r="15" fill="url(#gradient)" stroke="url(#border)" stroke-width="2"/>

  <!-- 光标图标 -->
  <path d="M8 8 L20 10 L14 16 L12 12 Z" fill="white" opacity="0.95"/>

  <!-- 小齿轮 -->
  <g transform="translate(22, 22)">
    <circle cx="0" cy="0" r="6" fill="white" opacity="0.9"/>
    <path d="M-3,-1 L-3,1 L-5,1 L-5,3 L-3,3 L-3,5 L-1,5 L-1,3 L1,3 L1,5 L3,5 L3,3 L5,3 L5,1 L3,1 L3,-1 L5,-1 L5,-3 L3,-3 L3,-5 L1,-5 L1,-3 L-1,-3 L-1,-5 L-3,-5 L-3,-3 L-5,-3 L-5,-1 Z" fill="#3B82F6"/>
    <circle cx="0" cy="0" r="2" fill="white"/>
  </g>

  <defs>
    <linearGradient id="gradient" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" style="stop-color:#6366F1;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#3B82F6;stop-opacity:1" />
    </linearGradient>
    <linearGradient id="border" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" style="stop-color:#8B5CF6;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#06B6D4;stop-opacity:1" />
    </linearGradient>
  </defs>
</svg>
