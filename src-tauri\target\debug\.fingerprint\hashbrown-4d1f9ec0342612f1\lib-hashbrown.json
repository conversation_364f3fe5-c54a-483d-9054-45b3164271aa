{"rustc": 3062648155896360161, "features": "[\"ahash\", \"allocator-api2\", \"default\", \"inline-more\"]", "declared_features": "[\"ahash\", \"alloc\", \"allocator-api2\", \"compiler_builtins\", \"core\", \"default\", \"equivalent\", \"inline-more\", \"nightly\", \"raw\", \"rayon\", \"rkyv\", \"rustc-dep-of-std\", \"rustc-internal-api\", \"serde\"]", "target": 9101038166729729440, "profile": 2241668132362809309, "path": 15452522703607766970, "deps": [[966925859616469517, "ahash", false, 17688649770921551507], [9150530836556604396, "allocator_api2", false, 10789581710923264976]], "local": [{"CheckDepInfo": {"dep_info": "debug\\.fingerprint\\hashbrown-4d1f9ec0342612f1\\dep-lib-hashbrown", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}