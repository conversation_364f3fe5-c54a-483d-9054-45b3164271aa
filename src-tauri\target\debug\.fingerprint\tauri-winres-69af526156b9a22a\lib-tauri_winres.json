{"rustc": 3062648155896360161, "features": "[]", "declared_features": "[]", "target": 2086567024422996381, "profile": 2225463790103693989, "path": 15301969588069595864, "deps": [[6941104557053927479, "embed_resource", false, 3020171638058765159], [12060164242600251039, "toml", false, 10661358208542864187]], "local": [{"CheckDepInfo": {"dep_info": "debug\\.fingerprint\\tauri-winres-69af526156b9a22a\\dep-lib-tauri_winres", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}