#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Cursor注册程序入口点
"""

import sys
import json
import os
from pathlib import Path

# 添加当前目录到path
current_dir = Path(__file__).parent
sys.path.insert(0, str(current_dir))

# 设置显示环境
os.environ.setdefault('DISPLAY', ':0')

def main():
    """主函数"""
    if len(sys.argv) < 2:
        print(json.dumps({
            "success": False,
            "error": "缺少参数，用法: cursor_register <email> [first_name] [last_name]"
        }))
        sys.exit(1)

    email = sys.argv[1]
    first_name = sys.argv[2] if len(sys.argv) > 2 else "Auto"
    last_name = sys.argv[3] if len(sys.argv) > 3 else "Generated"
    use_incognito = sys.argv[4] if len(sys.argv) > 4 else "true"

    try:
        # 导入manual_register模块并执行
        from manual_register import main as manual_main

        # 临时修改sys.argv来传递参数
        original_argv = sys.argv[:]
        sys.argv = ["manual_register.py", email, first_name, last_name, use_incognito]

        try:
            manual_main()
        finally:
            # 恢复原始argv
            sys.argv = original_argv

    except Exception as e:
        print(json.dumps({
            "success": False,
            "error": f"注册过程出错: {str(e)}"
        }, ensure_ascii=False))
        sys.exit(1)

if __name__ == "__main__":
    main()
