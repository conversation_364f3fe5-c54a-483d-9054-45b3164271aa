{"rustc": 3062648155896360161, "features": "[]", "declared_features": "[\"as-raw-xcb-connection\", \"bytemuck\", \"default\", \"drm\", \"fastrand\", \"kms\", \"memmap2\", \"rustix\", \"tiny-xlib\", \"wayland\", \"wayland-backend\", \"wayland-client\", \"wayland-dlopen\", \"wayland-sys\", \"x11\", \"x11-dlopen\", \"x11rb\"]", "target": 9174284484934603102, "profile": 2241668132362809309, "path": 11405596359119874869, "deps": [[376837177317575824, "build_script_build", false, 16203639596238174593], [4143744114649553716, "raw_window_handle", false, 3712108104936549517], [5986029879202738730, "log", false, 10945036430207440385], [10281541584571964250, "windows_sys", false, 14867831321290231463]], "local": [{"CheckDepInfo": {"dep_info": "debug\\.fingerprint\\softbuffer-e0886e28a53c22ad\\dep-lib-softbuffer", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}