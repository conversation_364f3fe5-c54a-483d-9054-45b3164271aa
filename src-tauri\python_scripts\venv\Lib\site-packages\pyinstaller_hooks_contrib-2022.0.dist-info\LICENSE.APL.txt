==========================================================
Licensing Terms for PyInstaller community runtime hooks
==========================================================

The PyInstaller community **runtime** hooks are licensed under the terms of the
Apache 2.0 Software License as published by the Apache Software Foundation.

These generally reside in "src/_pyinstaller_hooks_contrib/hooks/rthooks",
though please note that this license only applies to files which state so in the header, like:

# ------------------------------------------------------------------
# Copyright (c) 2021 PyInstaller Development Team.
#
# This file is distributed under the terms of the Apache License 2.0
#
# The full license is available in LICENSE.APL.txt, distributed with
# this software.
#
# SPDX-License-Identifier: Apache-2.0
# ------------------------------------------------------------------


Apache License 2.0
++++++++++++++++++

   Copyright 2020-2021 PyInstaller Development Team

   Licensed under the Apache License, Version 2.0 (the "License");
   you may not use this file except in compliance with the License.
   You may obtain a copy of the License at

       http://www.apache.org/licenses/LICENSE-2.0

   Unless required by applicable law or agreed to in writing, software
   distributed under the License is distributed on an "AS IS" BASIS,
   WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
   See the License for the specific language governing permissions and
   limitations under the License.
