{"rustc": 3062648155896360161, "features": "[\"brotli\", \"build\", \"cargo_metadata\", \"compression\", \"html-manipulation\", \"proc-macro2\", \"quote\", \"resources\", \"schema\", \"schemars\", \"swift-rs\", \"walkdir\"]", "declared_features": "[\"aes-gcm\", \"brotli\", \"build\", \"cargo_metadata\", \"compression\", \"config-json5\", \"config-toml\", \"getrandom\", \"html-manipulation\", \"isolation\", \"json5\", \"proc-macro2\", \"process-relaunch-dangerous-allow-symlink-macos\", \"quote\", \"resources\", \"schema\", \"schemars\", \"serialize-to-javascript\", \"swift-rs\", \"walkdir\"]", "target": 7530130812022395703, "profile": 2225463790103693989, "path": 16136054914536793718, "deps": [[373107762698212489, "proc_macro2", false, 11125204075046863589], [503635761244294217, "regex", false, 11842254416466190415], [1200537532907108615, "url<PERSON><PERSON>n", false, 14575826937199630780], [1678291836268844980, "brotli", false, 1798231122162274048], [2995469292676432503, "uuid", false, 210148040987626204], [4071963112282141418, "serde_with", false, 9992424964134099971], [4352886507220678900, "serde_json", false, 6670023159960378529], [4537297827336760846, "thiserror", false, 7987120693536890622], [4899080583175475170, "semver", false, 17625887480789864879], [5404511084185685755, "url", false, 11798837371836397836], [5986029879202738730, "log", false, 4993848995502101920], [6606131838865521726, "ctor", false, 10778789883276638683], [6913375703034175521, "schemars", false, 14222600190960689707], [7170110829644101142, "json_patch", false, 18002762306370759159], [9010263965687315507, "http", false, 2630624317178246918], [9293239362693504808, "glob", false, 4010883718105350449], [9689903380558560274, "serde", false, 8372695588916261890], [11207653606310558077, "anyhow", false, 4455137274005489050], [11655476559277113544, "cargo_metadata", false, 13754199746413721200], [11989259058781683633, "dunce", false, 15060689073163887837], [12060164242600251039, "toml", false, 10661358208542864187], [14232843520438415263, "html5ever", false, 17253620106789120948], [15088007382495681292, "kuchiki", false, 2545408953129768765], [15622660310229662834, "walkdir", false, 3961161375583282089], [15932120279885307830, "memchr", false, 8122729805812561546], [17146114186171651583, "infer", false, 7032098339538832190], [17183029615630212089, "serde_untagged", false, 6266057196472354233], [17186037756130803222, "phf", false, 15756393841576662097], [17990358020177143287, "quote", false, 15499431614064153038]], "local": [{"CheckDepInfo": {"dep_info": "debug\\.fingerprint\\tauri-utils-f3387b4f4b7df788\\dep-lib-tauri_utils", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}