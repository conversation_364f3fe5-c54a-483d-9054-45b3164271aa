{"rustc": 3062648155896360161, "features": "[\"brotli\", \"flate2\", \"gzip\", \"zlib\"]", "declared_features": "[\"all-algorithms\", \"brotli\", \"bzip2\", \"deflate\", \"deflate64\", \"flate2\", \"gzip\", \"libzstd\", \"lz4\", \"lzma\", \"xz\", \"xz-parallel\", \"xz2\", \"zlib\", \"zstd\", \"zstd-safe\", \"zstdmt\"]", "target": 2807176193865957057, "profile": 16163053410114657235, "path": 12647835654930748238, "deps": [[1678291836268844980, "brotli", false, 718431062575019176], [1906322745568073236, "pin_project_lite", false, 11136261259695911622], [7620660491849607393, "futures_core", false, 13771237771408866080], [13865646604939608930, "compression_core", false, 13607820298770437873], [15932120279885307830, "memchr", false, 12081632047258681516], [17772299992546037086, "flate2", false, 18414347701903889032]], "local": [{"CheckDepInfo": {"dep_info": "debug\\.fingerprint\\compression-codecs-4cb626ff571d15b6\\dep-lib-compression_codecs", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}