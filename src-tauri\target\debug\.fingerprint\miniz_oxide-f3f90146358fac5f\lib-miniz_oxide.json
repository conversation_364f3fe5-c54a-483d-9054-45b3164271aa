{"rustc": 3062648155896360161, "features": "[\"with-alloc\"]", "declared_features": "[\"alloc\", \"block-boundary\", \"core\", \"default\", \"rustc-dep-of-std\", \"serde\", \"simd\", \"simd-adler32\", \"std\", \"with-alloc\"]", "target": 8661567070972402511, "profile": 11250625435679592442, "path": 6324823206444540839, "deps": [[7911289239703230891, "adler2", false, 8428860922105835352]], "local": [{"CheckDepInfo": {"dep_info": "debug\\.fingerprint\\miniz_oxide-f3f90146358fac5f\\dep-lib-miniz_oxide", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}