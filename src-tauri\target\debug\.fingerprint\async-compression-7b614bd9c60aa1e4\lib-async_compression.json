{"rustc": 3062648155896360161, "features": "[\"brotli\", \"flate2\", \"gzip\", \"tokio\", \"zlib\"]", "declared_features": "[\"all\", \"all-algorithms\", \"all-implementations\", \"brotli\", \"bzip2\", \"deflate\", \"deflate64\", \"flate2\", \"futures-io\", \"gzip\", \"liblzma\", \"libzstd\", \"lz4\", \"lzma\", \"tokio\", \"xz\", \"xz-parallel\", \"xz2\", \"zlib\", \"zstd\", \"zstd-safe\", \"zstdmt\"]", "target": 7068030942456847288, "profile": 16163053410114657235, "path": 5183952152418375113, "deps": [[1678291836268844980, "brotli", false, 718431062575019176], [1906322745568073236, "pin_project_lite", false, 11136261259695911622], [7620660491849607393, "futures_core", false, 13771237771408866080], [9495956783373963623, "compression_codecs", false, 16547171226612019483], [13865646604939608930, "compression_core", false, 13607820298770437873], [15932120279885307830, "memchr", false, 12081632047258681516], [17531218394775549125, "tokio", false, 16899422864590576172], [17772299992546037086, "flate2", false, 18414347701903889032]], "local": [{"CheckDepInfo": {"dep_info": "debug\\.fingerprint\\async-compression-7b614bd9c60aa1e4\\dep-lib-async_compression", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}