{"rustc": 3062648155896360161, "features": "[\"clone-impls\", \"default\", \"derive\", \"extra-traits\", \"fold\", \"full\", \"parsing\", \"printing\", \"proc-macro\", \"quote\"]", "declared_features": "[\"clone-impls\", \"default\", \"derive\", \"extra-traits\", \"fold\", \"full\", \"parsing\", \"printing\", \"proc-macro\", \"quote\", \"test\", \"visit\", \"visit-mut\"]", "target": 11103975901103234717, "profile": 2225463790103693989, "path": 10443707201968231942, "deps": [[373107762698212489, "proc_macro2", false, 11125204075046863589], [1988483478007900009, "unicode_ident", false, 7906682142473215224], [2713742371683562785, "build_script_build", false, 15054060450918127281], [17990358020177143287, "quote", false, 15499431614064153038]], "local": [{"CheckDepInfo": {"dep_info": "debug\\.fingerprint\\syn-dfcc6ec9248fa7dc\\dep-lib-syn", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}