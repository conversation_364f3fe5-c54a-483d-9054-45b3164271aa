{"rustc": 3062648155896360161, "features": "[\"std\"]", "declared_features": "[\"bindgen\", \"compiler_builtins\", \"core\", \"dummy\", \"js-sys\", \"log\", \"rustc-dep-of-std\", \"std\", \"stdweb\", \"test-in-browser\", \"wasm-bindgen\"]", "target": 3140061874755240240, "profile": 2225463790103693989, "path": 15975731577623094524, "deps": [[5170503507811329045, "build_script_build", false, 11683974264211185619], [7843059260364151289, "cfg_if", false, 6955258511737294775]], "local": [{"CheckDepInfo": {"dep_info": "debug\\.fingerprint\\getrandom-1538e2fc14981a34\\dep-lib-getrandom", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}