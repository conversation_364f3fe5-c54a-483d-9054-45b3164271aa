{"rustc": 3062648155896360161, "features": "[\"common-controls-v6\", \"compression\", \"default\", \"dynamic-acl\", \"tauri-runtime-wry\", \"webkit2gtk\", \"webview2-com\", \"wry\", \"x11\"]", "declared_features": "[\"common-controls-v6\", \"compression\", \"config-json5\", \"config-toml\", \"custom-protocol\", \"data-url\", \"default\", \"devtools\", \"dynamic-acl\", \"http-range\", \"image\", \"image-ico\", \"image-png\", \"isolation\", \"linux-libxdo\", \"macos-private-api\", \"macos-proxy\", \"native-tls\", \"native-tls-vendored\", \"objc-exception\", \"process-relaunch-dangerous-allow-symlink-macos\", \"protocol-asset\", \"rustls-tls\", \"specta\", \"tauri-runtime-wry\", \"test\", \"tracing\", \"tray-icon\", \"unstable\", \"uuid\", \"webkit2gtk\", \"webview-data-url\", \"webview2-com\", \"wry\", \"x11\"]", "target": 12223948975794516716, "profile": 15657897354478470176, "path": 11091125854646426904, "deps": [[1200537532907108615, "url<PERSON><PERSON>n", false, 6248692474178931561], [1260461579271933187, "serialize_to_javascript", false, 8886895195550667694], [1967864351173319501, "muda", false, 8198458412835101699], [2013030631243296465, "webview2_com", false, 6175349319963950894], [3239934230994155792, "build_script_build", false, 4559112573748326344], [3331586631144870129, "getrandom", false, 17784719830796088768], [3899750328741010762, "tauri_runtime_wry", false, 67783960081989186], [4143744114649553716, "raw_window_handle", false, 15295701829235216055], [4352886507220678900, "serde_json", false, 11330634709981961599], [4537297827336760846, "thiserror", false, 6762184113965494642], [5404511084185685755, "url", false, 11920379897987289242], [5986029879202738730, "log", false, 1953282675189052718], [6537120525306722933, "tauri_macros", false, 12689295795476041669], [6803352382179706244, "percent_encoding", false, 493160013546494944], [9010263965687315507, "http", false, 219411552066842355], [9293239362693504808, "glob", false, 17651112888402818931], [9689903380558560274, "serde", false, 10055664706933609988], [10229185211513642314, "mime", false, 17231738861128014237], [11207653606310558077, "anyhow", false, 2795605351215414651], [11989259058781683633, "dunce", false, 132761268911456026], [12565293087094287914, "window_vibrancy", false, 17008595731119460574], [12986574360607194341, "serde_repr", false, 7346531942789701167], [13077543566650298139, "heck", false, 9103663505226094303], [14585479307175734061, "windows", false, 540296297399470663], [16727543399706004146, "cookie", false, 2532912088074131730], [16928111194414003569, "dirs", false, 16480909814453884573], [17233053221795943287, "tauri_utils", false, 17781450055114290046], [17531218394775549125, "tokio", false, 3748829182340658219], [18010483002580779355, "tauri_runtime", false, 14743274833973046269]], "local": [{"CheckDepInfo": {"dep_info": "debug\\.fingerprint\\tauri-a78093cbb8b471d3\\dep-lib-tauri", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}