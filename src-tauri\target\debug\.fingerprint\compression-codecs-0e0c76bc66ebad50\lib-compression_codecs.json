{"rustc": 3062648155896360161, "features": "[\"brotli\", \"flate2\", \"gzip\", \"zlib\"]", "declared_features": "[\"all-algorithms\", \"brotli\", \"bzip2\", \"deflate\", \"deflate64\", \"flate2\", \"gzip\", \"libzstd\", \"lz4\", \"lzma\", \"xz\", \"xz-parallel\", \"xz2\", \"zlib\", \"zstd\", \"zstd-safe\", \"zstdmt\"]", "target": 2807176193865957057, "profile": 1419271269840776899, "path": 12647835654930748238, "deps": [[1678291836268844980, "brotli", false, 6490841984202478381], [1906322745568073236, "pin_project_lite", false, 2544603237086067178], [7620660491849607393, "futures_core", false, 15780360714119166146], [13865646604939608930, "compression_core", false, 10963543668867509811], [15932120279885307830, "memchr", false, 589235425451624464], [17772299992546037086, "flate2", false, 16972944683250358959]], "local": [{"CheckDepInfo": {"dep_info": "debug\\.fingerprint\\compression-codecs-0e0c76bc66ebad50\\dep-lib-compression_codecs", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}