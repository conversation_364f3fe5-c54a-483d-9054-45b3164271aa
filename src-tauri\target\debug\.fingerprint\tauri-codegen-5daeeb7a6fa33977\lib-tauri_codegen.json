{"rustc": 3062648155896360161, "features": "[\"brotli\", \"compression\"]", "declared_features": "[\"brotli\", \"compression\", \"config-json5\", \"config-toml\", \"isolation\"]", "target": 17460618180909919773, "profile": 2225463790103693989, "path": 445383579239114737, "deps": [[373107762698212489, "proc_macro2", false, 11125204075046863589], [1678291836268844980, "brotli", false, 4300331577582835088], [2995469292676432503, "uuid", false, 951695421271510927], [4352886507220678900, "serde_json", false, 10385418511734645366], [4537297827336760846, "thiserror", false, 6762184113965494642], [4899080583175475170, "semver", false, 17625887480789864879], [5404511084185685755, "url", false, 806589115456336325], [7170110829644101142, "json_patch", false, 6027096731772322571], [7392050791754369441, "ico", false, 8899688626577584511], [9689903380558560274, "serde", false, 8372695588916261890], [9857275760291862238, "sha2", false, 332496150033082160], [12687914511023397207, "png", false, 17755251028591546099], [13077212702700853852, "base64", false, 6177770844656299697], [15622660310229662834, "walkdir", false, 8117565403321995056], [17233053221795943287, "tauri_utils", false, 10176838432830745087], [17332570067994900305, "syn", false, 6886913303237127862], [17990358020177143287, "quote", false, 15499431614064153038]], "local": [{"CheckDepInfo": {"dep_info": "debug\\.fingerprint\\tauri-codegen-5daeeb7a6fa33977\\dep-lib-tauri_codegen", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}