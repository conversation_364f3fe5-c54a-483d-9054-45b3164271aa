{"rustc": 3062648155896360161, "features": "[\"alloc\", \"default\", \"getrandom\", \"libc\", \"rand_chacha\", \"std\", \"std_rng\"]", "declared_features": "[\"alloc\", \"default\", \"getrandom\", \"libc\", \"log\", \"min_const_gen\", \"nightly\", \"packed_simd\", \"rand_chacha\", \"serde\", \"serde1\", \"simd_support\", \"small_rng\", \"std\", \"std_rng\"]", "target": 8827111241893198906, "profile": 2241668132362809309, "path": 2974432834400107671, "deps": [[1573238666360410412, "rand_chacha", false, 16478923489813035280], [18130209639506977569, "rand_core", false, 1717552395696492715]], "local": [{"CheckDepInfo": {"dep_info": "debug\\.fingerprint\\rand-dc139b1aa60b57de\\dep-lib-rand", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}