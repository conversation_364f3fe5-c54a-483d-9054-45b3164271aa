{"rustc": 3062648155896360161, "features": "[]", "declared_features": "[\"devtools\", \"macos-private-api\"]", "target": 10306386172444932100, "profile": 15657897354478470176, "path": 11105139912416421474, "deps": [[2013030631243296465, "webview2_com", false, 6175349319963950894], [4143744114649553716, "raw_window_handle", false, 15295701829235216055], [4352886507220678900, "serde_json", false, 11330634709981961599], [4537297827336760846, "thiserror", false, 6762184113965494642], [5404511084185685755, "url", false, 11920379897987289242], [7606335748176206944, "dpi", false, 11079597008299136588], [9010263965687315507, "http", false, 219411552066842355], [9689903380558560274, "serde", false, 10055664706933609988], [14585479307175734061, "windows", false, 540296297399470663], [16727543399706004146, "cookie", false, 2532912088074131730], [17233053221795943287, "tauri_utils", false, 17781450055114290046], [18010483002580779355, "build_script_build", false, 16698244333533178993]], "local": [{"CheckDepInfo": {"dep_info": "debug\\.fingerprint\\tauri-runtime-1120d91d829a53cc\\dep-lib-tauri_runtime", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}