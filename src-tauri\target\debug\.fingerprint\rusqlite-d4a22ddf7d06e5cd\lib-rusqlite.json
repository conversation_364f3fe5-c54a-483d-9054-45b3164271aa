{"rustc": 3062648155896360161, "features": "[\"bundled\", \"modern_sqlite\"]", "declared_features": "[\"array\", \"backup\", \"blob\", \"buildtime_bindgen\", \"bundled\", \"bundled-full\", \"bundled-sqlcipher\", \"bundled-sqlcipher-vendored-openssl\", \"bundled-windows\", \"chrono\", \"collation\", \"column_decltype\", \"csv\", \"csvtab\", \"extra_check\", \"functions\", \"hooks\", \"i128_blob\", \"in_gecko\", \"limits\", \"load_extension\", \"modern-full\", \"modern_sqlite\", \"release_memory\", \"serde_json\", \"series\", \"session\", \"sqlcipher\", \"time\", \"trace\", \"unlock_notify\", \"url\", \"uuid\", \"vtab\", \"wasm32-wasi-vfs\", \"window\", \"winsqlite3\", \"with-asan\"]", "target": 1382081207361365847, "profile": 15657897354478470176, "path": 4789161728817823676, "deps": [[1067686709987796982, "libsqlite3_sys", false, 16085340645811073507], [3405817021026194662, "hashlink", false, 5656473594530844844], [3666196340704888985, "smallvec", false, 17657741371438365037], [5510864063823219921, "fallible_streaming_iterator", false, 194226790035813530], [15840480199427237938, "bitflags", false, 3675898300379378199], [17725626451704002459, "fallible_iterator", false, 17840157788567068798]], "local": [{"CheckDepInfo": {"dep_info": "debug\\.fingerprint\\rusqlite-d4a22ddf7d06e5cd\\dep-lib-rusqlite", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}