.. image:: https://img.shields.io/pypi/v/setuptools.svg
   :target: https://pypi.org/project/setuptools

.. image:: https://readthedocs.org/projects/setuptools/badge/?version=latest
    :target: https://setuptools.readthedocs.io

.. image:: https://img.shields.io/travis/pypa/setuptools/master.svg?label=Linux%20build%20%40%20Travis%20CI
   :target: https://travis-ci.org/pypa/setuptools

.. image:: https://img.shields.io/appveyor/ci/jaraco/setuptools/master.svg?label=Windows%20build%20%40%20Appveyor
   :target: https://ci.appveyor.com/project/jaraco/setuptools/branch/master

.. image:: https://img.shields.io/pypi/pyversions/setuptools.svg

See the `Installation Instructions
<https://packaging.python.org/installing/>`_ in the Python Packaging
User's Guide for instructions on installing, upgrading, and uninstalling
Setuptools.

The project is `maintained at GitHub <https://github.com/pypa/setuptools>`_.

Questions and comments should be directed to the `distutils-sig
mailing list <http://mail.python.org/pipermail/distutils-sig/>`_.
Bug reports and especially tested patches may be
submitted directly to the `bug tracker
<https://github.com/pypa/setuptools/issues>`_.


Code of Conduct
---------------

Everyone interacting in the setuptools project's codebases, issue trackers,
chat rooms, and mailing lists is expected to follow the
`PyPA Code of Conduct <https://www.pypa.io/en/latest/code-of-conduct/>`_.


