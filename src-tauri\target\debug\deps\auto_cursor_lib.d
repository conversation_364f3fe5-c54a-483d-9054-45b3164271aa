E:\前端\auto-cursor\src-tauri\target\debug\deps\auto_cursor_lib.d: src\lib.rs src\account_manager.rs src\auth_checker.rs src\machine_id.rs E:\前端\auto-cursor\src-tauri\target\debug\build\auto-cursor-d18ec1ad16d3c7e9\out/5594da24b2af250f4496f85eba12d473b204f7f26bfa9c81cca93302e4e9c0bb

E:\前端\auto-cursor\src-tauri\target\debug\deps\auto_cursor_lib.lib: src\lib.rs src\account_manager.rs src\auth_checker.rs src\machine_id.rs E:\前端\auto-cursor\src-tauri\target\debug\build\auto-cursor-d18ec1ad16d3c7e9\out/5594da24b2af250f4496f85eba12d473b204f7f26bfa9c81cca93302e4e9c0bb

E:\前端\auto-cursor\src-tauri\target\debug\deps\auto_cursor_lib.dll: src\lib.rs src\account_manager.rs src\auth_checker.rs src\machine_id.rs E:\前端\auto-cursor\src-tauri\target\debug\build\auto-cursor-d18ec1ad16d3c7e9\out/5594da24b2af250f4496f85eba12d473b204f7f26bfa9c81cca93302e4e9c0bb

E:\前端\auto-cursor\src-tauri\target\debug\deps\libauto_cursor_lib.rlib: src\lib.rs src\account_manager.rs src\auth_checker.rs src\machine_id.rs E:\前端\auto-cursor\src-tauri\target\debug\build\auto-cursor-d18ec1ad16d3c7e9\out/5594da24b2af250f4496f85eba12d473b204f7f26bfa9c81cca93302e4e9c0bb

src\lib.rs:
src\account_manager.rs:
src\auth_checker.rs:
src\machine_id.rs:
E:\前端\auto-cursor\src-tauri\target\debug\build\auto-cursor-d18ec1ad16d3c7e9\out/5594da24b2af250f4496f85eba12d473b204f7f26bfa9c81cca93302e4e9c0bb:

# env-dep:CARGO_PKG_AUTHORS=you
# env-dep:CARGO_PKG_DESCRIPTION=A Tauri App
# env-dep:CARGO_PKG_NAME=auto-cursor
# env-dep:OUT_DIR=E:\\前端\\auto-cursor\\src-tauri\\target\\debug\\build\\auto-cursor-d18ec1ad16d3c7e9\\out
