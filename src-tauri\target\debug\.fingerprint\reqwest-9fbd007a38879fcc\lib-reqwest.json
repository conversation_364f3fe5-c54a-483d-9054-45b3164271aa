{"rustc": 3062648155896360161, "features": "[\"__tls\", \"async-compression\", \"brotli\", \"default\", \"default-tls\", \"deflate\", \"gzip\", \"hyper-tls\", \"json\", \"native-tls-crate\", \"serde_json\", \"tokio-native-tls\", \"tokio-util\"]", "declared_features": "[\"__internal_proxy_sys_no_cache\", \"__rustls\", \"__tls\", \"async-compression\", \"blocking\", \"brotli\", \"cookie_crate\", \"cookie_store\", \"cookies\", \"default\", \"default-tls\", \"deflate\", \"futures-channel\", \"gzip\", \"h3\", \"h3-quinn\", \"hickory-dns\", \"hickory-resolver\", \"http3\", \"hyper-rustls\", \"hyper-tls\", \"json\", \"mime_guess\", \"multipart\", \"native-tls\", \"native-tls-alpn\", \"native-tls-crate\", \"native-tls-vendored\", \"quinn\", \"rustls\", \"rustls-native-certs\", \"rustls-tls\", \"rustls-tls-manual-roots\", \"rustls-tls-native-roots\", \"rustls-tls-webpki-roots\", \"serde_json\", \"socks\", \"stream\", \"tokio-native-tls\", \"tokio-rustls\", \"tokio-socks\", \"tokio-util\", \"trust-dns\", \"wasm-streams\", \"webpki-roots\"]", "target": 16585426341985349207, "profile": 2241668132362809309, "path": 10775075817940294356, "deps": [[95042085696191081, "ipnet", false, 14357894060532463972], [264090853244900308, "sync_wrapper", false, 2616070729202848711], [784494742817713399, "tower_service", false, 2413096805617494174], [1906322745568073236, "pin_project_lite", false, 11136261259695911622], [3722963349756955755, "once_cell", false, 4464787853665521902], [4352886507220678900, "serde_json", false, 16540324934345454808], [4405182208873388884, "http", false, 12008679850274588415], [5404511084185685755, "url", false, 17090050064083276578], [5986029879202738730, "log", false, 10945036430207440385], [6803352382179706244, "percent_encoding", false, 2508400917264263915], [7414427314941361239, "hyper", false, 6031850645787003981], [7620660491849607393, "futures_core", false, 13771237771408866080], [8405603588346937335, "winreg", false, 15721091670357367292], [8915503303801890683, "http_body", false, 8548618970148914048], [9689903380558560274, "serde", false, 11450851296298991122], [10229185211513642314, "mime", false, 569491473092530045], [10629569228670356391, "futures_util", false, 16807365093062022912], [12186126227181294540, "tokio_native_tls", false, 5395992381874141747], [12367227501898450486, "hyper_tls", false, 7370147967289785294], [12590489844211591007, "async_compression", false, 7100905318045271134], [13763625454224483636, "h2", false, 4772068392028113545], [14564311161534545801, "encoding_rs", false, 618561209450345572], [15894030960229394068, "tokio_util", false, 17907598886699470905], [16066129441945555748, "bytes", false, 5104614103645194477], [16311359161338405624, "rustls_pemfile", false, 5123755408630092420], [16542808166767769916, "serde_urlencoded", false, 7327285027869544225], [16785601910559813697, "native_tls_crate", false, 17678603423571793157], [17531218394775549125, "tokio", false, 16899422864590576172], [18066890886671768183, "base64", false, 4259307100133726979]], "local": [{"CheckDepInfo": {"dep_info": "debug\\.fingerprint\\reqwest-9fbd007a38879fcc\\dep-lib-reqwest", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}