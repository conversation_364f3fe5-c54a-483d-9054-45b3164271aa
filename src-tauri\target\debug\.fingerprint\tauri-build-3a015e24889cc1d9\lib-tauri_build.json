{"rustc": 3062648155896360161, "features": "[\"config-json\", \"default\"]", "declared_features": "[\"codegen\", \"config-json\", \"config-json5\", \"config-toml\", \"default\", \"isolation\", \"quote\", \"tauri-codegen\"]", "target": 1006236803848883740, "profile": 2225463790103693989, "path": 5900569307827957037, "deps": [[4352886507220678900, "serde_json", false, 6670023159960378529], [4824857623768494398, "cargo_toml", false, 17202742059660710635], [4899080583175475170, "semver", false, 17625887480789864879], [5165059047667588304, "tauri_winres", false, 12358393833868405183], [6913375703034175521, "schemars", false, 14222600190960689707], [7170110829644101142, "json_patch", false, 18002762306370759159], [9293239362693504808, "glob", false, 4010883718105350449], [9689903380558560274, "serde", false, 8372695588916261890], [11207653606310558077, "anyhow", false, 4455137274005489050], [12060164242600251039, "toml", false, 10661358208542864187], [13077543566650298139, "heck", false, 14104576484858509289], [15622660310229662834, "walkdir", false, 3961161375583282089], [16928111194414003569, "dirs", false, 7459353689574313537], [17233053221795943287, "tauri_utils", false, 11092907689118060566]], "local": [{"CheckDepInfo": {"dep_info": "debug\\.fingerprint\\tauri-build-3a015e24889cc1d9\\dep-lib-tauri_build", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}