{"rustc": 3062648155896360161, "features": "[\"common-controls-v6\", \"compression\", \"default\", \"dynamic-acl\", \"tauri-runtime-wry\", \"webkit2gtk\", \"webview2-com\", \"wry\", \"x11\"]", "declared_features": "[\"common-controls-v6\", \"compression\", \"config-json5\", \"config-toml\", \"custom-protocol\", \"data-url\", \"default\", \"devtools\", \"dynamic-acl\", \"http-range\", \"image\", \"image-ico\", \"image-png\", \"isolation\", \"linux-libxdo\", \"macos-private-api\", \"macos-proxy\", \"native-tls\", \"native-tls-vendored\", \"objc-exception\", \"process-relaunch-dangerous-allow-symlink-macos\", \"protocol-asset\", \"rustls-tls\", \"specta\", \"tauri-runtime-wry\", \"test\", \"tracing\", \"tray-icon\", \"unstable\", \"uuid\", \"webkit2gtk\", \"webview-data-url\", \"webview2-com\", \"wry\", \"x11\"]", "target": 12223948975794516716, "profile": 2241668132362809309, "path": 11091125854646426904, "deps": [[1200537532907108615, "url<PERSON><PERSON>n", false, 10720567922898033911], [1260461579271933187, "serialize_to_javascript", false, 7009650353107386883], [1967864351173319501, "muda", false, 13580298786339520938], [2013030631243296465, "webview2_com", false, 18122369991519328561], [3239934230994155792, "build_script_build", false, 13018298264037906891], [3331586631144870129, "getrandom", false, 14000836083979851245], [3899750328741010762, "tauri_runtime_wry", false, 10639215281750398377], [4143744114649553716, "raw_window_handle", false, 3712108104936549517], [4352886507220678900, "serde_json", false, 16540324934345454808], [4537297827336760846, "thiserror", false, 12581723110571688579], [5404511084185685755, "url", false, 17090050064083276578], [5986029879202738730, "log", false, 10945036430207440385], [6537120525306722933, "tauri_macros", false, 6844834043771043148], [6803352382179706244, "percent_encoding", false, 2508400917264263915], [9010263965687315507, "http", false, 4556469098080637687], [9293239362693504808, "glob", false, 14452371957826983276], [9689903380558560274, "serde", false, 11450851296298991122], [10229185211513642314, "mime", false, 569491473092530045], [11207653606310558077, "anyhow", false, 3120685154372012934], [11989259058781683633, "dunce", false, 9434174050560423366], [12565293087094287914, "window_vibrancy", false, 4172575368286086516], [12986574360607194341, "serde_repr", false, 7346531942789701167], [13077543566650298139, "heck", false, 3012008791770047322], [14585479307175734061, "windows", false, 457994763200677128], [16727543399706004146, "cookie", false, 1546025980771397503], [16928111194414003569, "dirs", false, 6536855993178419061], [17233053221795943287, "tauri_utils", false, 56706992117261356], [17531218394775549125, "tokio", false, 16899422864590576172], [18010483002580779355, "tauri_runtime", false, 9195745845183656088]], "local": [{"CheckDepInfo": {"dep_info": "debug\\.fingerprint\\tauri-24a976e60f011b97\\dep-lib-tauri", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}