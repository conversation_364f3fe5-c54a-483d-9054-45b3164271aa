{"rustc": 3062648155896360161, "features": "[\"std\"]", "declared_features": "[\"async-await\", \"attributes\", \"default\", \"log\", \"log-always\", \"max_level_debug\", \"max_level_error\", \"max_level_info\", \"max_level_off\", \"max_level_trace\", \"max_level_warn\", \"release_max_level_debug\", \"release_max_level_error\", \"release_max_level_info\", \"release_max_level_off\", \"release_max_level_trace\", \"release_max_level_warn\", \"std\", \"tracing-attributes\", \"valuable\"]", "target": 5568135053145998517, "profile": 6355579909791343455, "path": 6044502191206015230, "deps": [[1906322745568073236, "pin_project_lite", false, 2544603237086067178], [3424551429995674438, "tracing_core", false, 3058423577524194188]], "local": [{"CheckDepInfo": {"dep_info": "debug\\.fingerprint\\tracing-7316b89bec376225\\dep-lib-tracing", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}