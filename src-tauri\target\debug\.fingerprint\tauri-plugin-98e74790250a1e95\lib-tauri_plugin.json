{"rustc": 3062648155896360161, "features": "[\"build\"]", "declared_features": "[\"build\", \"runtime\"]", "target": 15996119522804316622, "profile": 2225463790103693989, "path": 13153336313544039088, "deps": [[4352886507220678900, "serde_json", false, 10385418511734645366], [6913375703034175521, "schemars", false, 13845663403254456321], [9293239362693504808, "glob", false, 17651112888402818931], [9689903380558560274, "serde", false, 8372695588916261890], [11207653606310558077, "anyhow", false, 2795605351215414651], [12060164242600251039, "toml", false, 344541517394915406], [15622660310229662834, "walkdir", false, 8117565403321995056], [17233053221795943287, "tauri_utils", false, 10176838432830745087]], "local": [{"CheckDepInfo": {"dep_info": "debug\\.fingerprint\\tauri-plugin-98e74790250a1e95\\dep-lib-tauri_plugin", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}