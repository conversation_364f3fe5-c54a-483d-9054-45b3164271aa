{"rustc": 3062648155896360161, "features": "[\"common-controls-v6\", \"x11\"]", "declared_features": "[\"common-controls-v6\", \"default\", \"devtools\", \"macos-private-api\", \"macos-proxy\", \"objc-exception\", \"tracing\", \"unstable\", \"x11\"]", "target": 1901661049345253480, "profile": 15657897354478470176, "path": 8020507221556070678, "deps": [[376837177317575824, "softbuffer", false, 13723104308985334594], [2013030631243296465, "webview2_com", false, 6175349319963950894], [2172092324659420098, "tao", false, 7078273932775343230], [3722963349756955755, "once_cell", false, 14459459940105802541], [3899750328741010762, "build_script_build", false, 14409777034687062093], [4143744114649553716, "raw_window_handle", false, 15295701829235216055], [5404511084185685755, "url", false, 11920379897987289242], [5986029879202738730, "log", false, 1953282675189052718], [9010263965687315507, "http", false, 219411552066842355], [14585479307175734061, "windows", false, 540296297399470663], [15302940006583996851, "wry", false, 17512998765769304504], [17233053221795943287, "tauri_utils", false, 17781450055114290046], [18010483002580779355, "tauri_runtime", false, 14743274833973046269]], "local": [{"CheckDepInfo": {"dep_info": "debug\\.fingerprint\\tauri-runtime-wry-2d9496964acb1561\\dep-lib-tauri_runtime_wry", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}