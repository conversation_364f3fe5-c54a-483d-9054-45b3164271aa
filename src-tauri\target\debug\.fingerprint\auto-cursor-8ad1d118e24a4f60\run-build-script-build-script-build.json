{"rustc": 3062648155896360161, "features": "", "declared_features": "", "target": 0, "profile": 0, "path": 0, "deps": [[12602218467368646123, "build_script_build", false, 11245828803573077065], [3239934230994155792, "build_script_build", false, 13018298264037906891], [14285978758320820277, "build_script_build", false, 2169366267432927395], [16429266147849286097, "build_script_build", false, 10033690818955226031]], "local": [{"RerunIfChanged": {"output": "debug\\build\\auto-cursor-8ad1d118e24a4f60\\output", "paths": ["tauri.conf.json", "capabilities", "pyBuild\\macos\\README.md", "pyBuild\\macos\\cursor_register", "pyBuild\\windows\\README.md", "pyBuild\\windows\\cursor_register.exe"]}}, {"RerunIfEnvChanged": {"var": "TAURI_CONFIG", "val": null}}, {"RerunIfEnvChanged": {"var": "REMOVE_UNUSED_COMMANDS", "val": null}}], "rustflags": [], "config": 0, "compile_kind": 0}