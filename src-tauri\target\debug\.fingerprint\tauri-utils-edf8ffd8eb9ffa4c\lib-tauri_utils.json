{"rustc": 3062648155896360161, "features": "[\"brotli\", \"compression\", \"resources\", \"walkdir\"]", "declared_features": "[\"aes-gcm\", \"brotli\", \"build\", \"cargo_metadata\", \"compression\", \"config-json5\", \"config-toml\", \"getrandom\", \"html-manipulation\", \"isolation\", \"json5\", \"proc-macro2\", \"process-relaunch-dangerous-allow-symlink-macos\", \"quote\", \"resources\", \"schema\", \"schemars\", \"serialize-to-javascript\", \"swift-rs\", \"walkdir\"]", "target": 7530130812022395703, "profile": 2241668132362809309, "path": 16136054914536793718, "deps": [[503635761244294217, "regex", false, 2754097746849034404], [1200537532907108615, "url<PERSON><PERSON>n", false, 10720567922898033911], [1678291836268844980, "brotli", false, 718431062575019176], [2995469292676432503, "uuid", false, 8269864632713534960], [4071963112282141418, "serde_with", false, 17907124981438947028], [4352886507220678900, "serde_json", false, 16540324934345454808], [4537297827336760846, "thiserror", false, 12581723110571688579], [4899080583175475170, "semver", false, 5104579223376873062], [5404511084185685755, "url", false, 17090050064083276578], [5986029879202738730, "log", false, 10945036430207440385], [6606131838865521726, "ctor", false, 10778789883276638683], [7170110829644101142, "json_patch", false, 13842417346818507176], [9010263965687315507, "http", false, 4556469098080637687], [9293239362693504808, "glob", false, 14452371957826983276], [9689903380558560274, "serde", false, 11450851296298991122], [11207653606310558077, "anyhow", false, 3120685154372012934], [11989259058781683633, "dunce", false, 9434174050560423366], [12060164242600251039, "toml", false, 10549786238200611990], [15622660310229662834, "walkdir", false, 17297148065737910699], [15932120279885307830, "memchr", false, 12081632047258681516], [17146114186171651583, "infer", false, 2906669556290184307], [17183029615630212089, "serde_untagged", false, 18423271487912404894], [17186037756130803222, "phf", false, 15174515364666220339]], "local": [{"CheckDepInfo": {"dep_info": "debug\\.fingerprint\\tauri-utils-edf8ffd8eb9ffa4c\\dep-lib-tauri_utils", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}