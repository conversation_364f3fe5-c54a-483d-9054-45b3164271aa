{"rustc": 3062648155896360161, "features": "[\"brotli\", \"compression\", \"resources\", \"walkdir\"]", "declared_features": "[\"aes-gcm\", \"brotli\", \"build\", \"cargo_metadata\", \"compression\", \"config-json5\", \"config-toml\", \"getrandom\", \"html-manipulation\", \"isolation\", \"json5\", \"proc-macro2\", \"process-relaunch-dangerous-allow-symlink-macos\", \"quote\", \"resources\", \"schema\", \"schemars\", \"serialize-to-javascript\", \"swift-rs\", \"walkdir\"]", "target": 7530130812022395703, "profile": 15657897354478470176, "path": 16136054914536793718, "deps": [[503635761244294217, "regex", false, 10349561990116727839], [1200537532907108615, "url<PERSON><PERSON>n", false, 6248692474178931561], [1678291836268844980, "brotli", false, 6490841984202478381], [2995469292676432503, "uuid", false, 7682430811029921136], [4071963112282141418, "serde_with", false, 7568194178612887850], [4352886507220678900, "serde_json", false, 11330634709981961599], [4537297827336760846, "thiserror", false, 6762184113965494642], [4899080583175475170, "semver", false, 5687146627614205336], [5404511084185685755, "url", false, 11920379897987289242], [5986029879202738730, "log", false, 1953282675189052718], [6606131838865521726, "ctor", false, 10778789883276638683], [7170110829644101142, "json_patch", false, 4419294415967822853], [9010263965687315507, "http", false, 219411552066842355], [9293239362693504808, "glob", false, 17651112888402818931], [9689903380558560274, "serde", false, 10055664706933609988], [11207653606310558077, "anyhow", false, 2795605351215414651], [11989259058781683633, "dunce", false, 132761268911456026], [12060164242600251039, "toml", false, 682374820126565088], [15622660310229662834, "walkdir", false, 12744605715100422034], [15932120279885307830, "memchr", false, 589235425451624464], [17146114186171651583, "infer", false, 18178146407482289776], [17183029615630212089, "serde_untagged", false, 8335560838677312931], [17186037756130803222, "phf", false, 5199611046788859798]], "local": [{"CheckDepInfo": {"dep_info": "debug\\.fingerprint\\tauri-utils-d429a61f70d7794e\\dep-lib-tauri_utils", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}