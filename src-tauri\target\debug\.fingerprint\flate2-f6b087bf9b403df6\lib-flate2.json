{"rustc": 3062648155896360161, "features": "[\"any_impl\", \"default\", \"miniz_oxide\", \"rust_backend\"]", "declared_features": "[\"any_impl\", \"any_zlib\", \"cloudflare-zlib-sys\", \"cloudflare_zlib\", \"default\", \"libz-ng-sys\", \"libz-rs-sys\", \"libz-sys\", \"miniz-sys\", \"miniz_oxide\", \"rust_backend\", \"zlib\", \"zlib-default\", \"zlib-ng\", \"zlib-ng-compat\", \"zlib-rs\"]", "target": 6173716359330453699, "profile": 15657897354478470176, "path": 15451759657200104018, "deps": [[7312356825837975969, "crc32fast", false, 10816493535128511655], [7636735136738807108, "miniz_oxide", false, 6108077434657757177]], "local": [{"CheckDepInfo": {"dep_info": "debug\\.fingerprint\\flate2-f6b087bf9b403df6\\dep-lib-flate2", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}