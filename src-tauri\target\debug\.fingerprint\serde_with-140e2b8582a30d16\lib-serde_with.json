{"rustc": 3062648155896360161, "features": "[\"alloc\", \"default\", \"macros\", \"std\"]", "declared_features": "[\"alloc\", \"base64\", \"chrono\", \"chrono_0_4\", \"default\", \"guide\", \"hashbrown_0_14\", \"hashbrown_0_15\", \"hex\", \"indexmap\", \"indexmap_1\", \"indexmap_2\", \"json\", \"macros\", \"schemars_0_8\", \"schemars_0_9\", \"schemars_1\", \"std\", \"time_0_3\"]", "target": 10448421281463538527, "profile": 5290030462671737236, "path": 15008619074305004284, "deps": [[7026957619838884710, "serde_with_macros", false, 7509135413182785899], [9689903380558560274, "serde", false, 10055664706933609988], [16257276029081467297, "serde_derive", false, 12708777898324683608]], "local": [{"CheckDepInfo": {"dep_info": "debug\\.fingerprint\\serde_with-140e2b8582a30d16\\dep-lib-serde_with", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}