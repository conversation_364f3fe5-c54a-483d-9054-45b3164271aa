{"rustc": 3062648155896360161, "features": "[\"alloc\", \"derive\"]", "declared_features": "[\"alloc\", \"default\", \"derive\"]", "target": 723370850876025358, "profile": 2241668132362809309, "path": 10466995921066483684, "deps": [[4022439902832367970, "zerofrom_derive", false, 1130019122169659545]], "local": [{"CheckDepInfo": {"dep_info": "debug\\.fingerprint\\zerofrom-fa4dd68d402b8741\\dep-lib-zerofrom", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}